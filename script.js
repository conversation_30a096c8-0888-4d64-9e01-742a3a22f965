// Enhanced animations and interactions for the Influence360 landing page

document.addEventListener('DOMContentLoaded', function () {
    // Add smooth scrolling and enhanced animations
    initializeAnimations();
    setupInteractiveElements();
    createParticleEffect();
});

function initializeAnimations() {
    // Animate elements on scroll/load
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe feature cards
    document.querySelectorAll('.feature').forEach(feature => {
        feature.style.opacity = '0';
        feature.style.transform = 'translateY(30px)';
        feature.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(feature);
    });

    // Stagger animation for features
    document.querySelectorAll('.feature').forEach((feature, index) => {
        setTimeout(() => {
            feature.style.opacity = '1';
            feature.style.transform = 'translateY(0)';
        }, index * 200);
    });
}

function setupInteractiveElements() {
    // Enhanced CTA button interaction
    const ctaButton = document.querySelector('.cta-button');

    ctaButton.addEventListener('click', function (e) {
        e.preventDefault();

        // Create ripple effect
        const ripple = document.createElement('span');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');

        this.appendChild(ripple);

        // Show coming soon message
        showComingSoonModal();

        setTimeout(() => {
            ripple.remove();
        }, 600);
    });

    // Add ripple effect styles
    const style = document.createElement('style');
    style.textContent = `
        .cta-button {
            position: relative;
            overflow: hidden;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: linear-gradient(135deg, #1a0033, #2d1b69);
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            transform: scale(0.8);
            transition: transform 0.3s ease;
        }

        .modal.show .modal-content {
            transform: scale(1);
        }

        .modal h3 {
            color: #a855f7;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }

        .modal p {
            color: #cbd5e1;
            margin-bottom: 20px;
        }

        .modal button {
            background: linear-gradient(135deg, #a855f7, #ec4899);
            border: none;
            padding: 10px 25px;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            font-weight: 600;
        }
    `;
    document.head.appendChild(style);

    // Add responsive mouse movement effect to floating objects
    document.addEventListener('mousemove', (e) => {
        // Only apply mouse effects on larger screens
        if (window.innerWidth <= 768) return;

        const objects = document.querySelectorAll('.floating-object');
        const mouseX = e.clientX / window.innerWidth;
        const mouseY = e.clientY / window.innerHeight;

        objects.forEach((obj, index) => {
            // Scale effect based on screen size
            const screenScale = window.innerWidth > 1200 ? 1 : 0.6;
            const speed = (index + 1) * 0.3 * screenScale;
            const x = (mouseX - 0.5) * speed;
            const y = (mouseY - 0.5) * speed;

            // Reset transform and apply new one to avoid accumulation
            const currentTransform = obj.style.transform.replace(/translate\([^)]*\)/g, '');
            obj.style.transform = currentTransform + ` translate(${x}px, ${y}px)`;
        });
    });
}

function showComingSoonModal() {
    // Create modal if it doesn't exist
    let modal = document.querySelector('.modal');
    if (!modal) {
        modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <h3>🚀 Coming Soon!</h3>
                <p>We're working hard to bring you the future of Web3 marketing.<br>
                Stay tuned for updates!</p>
                <button onclick="closeModal()">Got it!</button>
            </div>
        `;
        document.body.appendChild(modal);
    }

    modal.classList.add('show');

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal();
        }
    });
}

function closeModal() {
    const modal = document.querySelector('.modal');
    if (modal) {
        modal.classList.remove('show');
    }
}

function createParticleEffect() {
    // Create responsive particle effect
    const particleContainer = document.createElement('div');
    particleContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1;
    `;

    document.body.appendChild(particleContainer);

    // Responsive particle count based on screen size
    const getParticleCount = () => {
        if (window.innerWidth <= 480) return 8;
        if (window.innerWidth <= 768) return 12;
        if (window.innerWidth <= 1200) return 16;
        return 20;
    };

    // Create particles
    for (let i = 0; i < getParticleCount(); i++) {
        createParticle(particleContainer);
    }
}

function createParticle(container) {
    const particle = document.createElement('div');

    // Responsive particle sizing
    const getParticleSize = () => {
        if (window.innerWidth <= 480) return Math.random() * 2 + 1;
        if (window.innerWidth <= 768) return Math.random() * 3 + 1.5;
        return Math.random() * 4 + 2;
    };

    const size = getParticleSize();
    const x = Math.random() * window.innerWidth;
    const y = Math.random() * window.innerHeight;

    // Responsive animation duration
    const getDuration = () => {
        if (window.innerWidth <= 480) return Math.random() * 25 + 15;
        if (window.innerWidth <= 768) return Math.random() * 22 + 12;
        return Math.random() * 20 + 10;
    };

    const duration = getDuration();

    // Responsive opacity based on screen size
    const getOpacity = () => {
        if (window.innerWidth <= 480) return 0.3;
        if (window.innerWidth <= 768) return 0.4;
        return 0.6;
    };

    particle.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        background: radial-gradient(circle, rgba(168, 85, 247, ${getOpacity()}), transparent);
        border-radius: 50%;
        left: ${x}px;
        top: ${y}px;
        animation: float-particle ${duration}s linear infinite;
    `;

    container.appendChild(particle);

    // Add responsive particle animation
    if (!document.querySelector('#particle-styles')) {
        const particleStyles = document.createElement('style');
        particleStyles.id = 'particle-styles';
        particleStyles.textContent = `
            @keyframes float-particle {
                0% {
                    transform: translateY(0px) rotate(0deg);
                    opacity: 0;
                }
                10% {
                    opacity: 1;
                }
                90% {
                    opacity: 1;
                }
                100% {
                    transform: translateY(-100vh) rotate(360deg);
                    opacity: 0;
                }
            }

            @media (max-width: 768px) {
                @keyframes float-particle {
                    0% {
                        transform: translateY(0px);
                        opacity: 0;
                    }
                    15% {
                        opacity: 1;
                    }
                    85% {
                        opacity: 1;
                    }
                    100% {
                        transform: translateY(-100vh);
                        opacity: 0;
                    }
                }
            }
        `;
        document.head.appendChild(particleStyles);
    }

    // Remove particle after animation and create new one
    setTimeout(() => {
        particle.remove();
        createParticle(container);
    }, duration * 1000);
}

// Add comprehensive window resize handler for responsive animations
let resizeTimeout;
window.addEventListener('resize', () => {
    // Debounce resize events
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
        // Recalculate particle positions on resize
        const particles = document.querySelectorAll('[style*="float-particle"]');
        particles.forEach(particle => {
            const x = Math.random() * window.innerWidth;
            particle.style.left = x + 'px';
        });

        // Reset floating object transforms to prevent positioning issues
        const floatingObjects = document.querySelectorAll('.floating-object');
        floatingObjects.forEach(obj => {
            obj.style.transform = obj.style.transform.replace(/translate\([^)]*\)/g, '');
        });

        // Recreate particle effect with new count if needed
        const currentParticleCount = particles.length;
        const newParticleCount = (() => {
            if (window.innerWidth <= 480) return 8;
            if (window.innerWidth <= 768) return 12;
            if (window.innerWidth <= 1200) return 16;
            return 20;
        })();

        // Adjust particle count if screen size changed significantly
        if (Math.abs(currentParticleCount - newParticleCount) > 4) {
            // Remove excess particles or add more as needed
            if (currentParticleCount > newParticleCount) {
                for (let i = currentParticleCount - 1; i >= newParticleCount; i--) {
                    if (particles[i]) particles[i].remove();
                }
            }
        }
    }, 250);
});

// Make closeModal function global
window.closeModal = closeModal;
