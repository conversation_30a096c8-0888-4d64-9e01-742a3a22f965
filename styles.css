/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #1a0033 0%, #2d1b69 25%, #0f0f23 50%, #4c1d95 75%, #1a0033 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    color: #ffffff;
    overflow-x: hidden;
    min-height: 100vh;
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

/* Animated Background Objects */
.animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-object {
    position: absolute;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 15px 20px;
    animation-duration: 20s;
    animation-iteration-count: infinite;
    animation-timing-function: ease-in-out;
    transition: all 0.3s ease;
    will-change: transform;
    z-index: 1;
}

.obj-content {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    font-size: 14px;
}

.obj-icon {
    font-size: 20px;
}

/* Individual object styling and animations */
.influencer-obj {
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2));
    top: 15%;
    left: 8%;
    animation-name: float1;
}

.brands-obj {
    background: linear-gradient(135deg, rgba(236, 72, 153, 0.2), rgba(59, 130, 246, 0.2));
    top: 35%;
    left: 75%;
    animation-name: float3;
}

@keyframes float1 {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }

    33% {
        transform: translateY(-30px) rotate(5deg);
    }

    66% {
        transform: translateY(20px) rotate(-3deg);
    }
}

@keyframes float3 {

    0%,
    100% {
        transform: translateY(0px) translateX(0px);
    }

    25% {
        transform: translateY(-20px) translateX(-15px);
    }

    75% {
        transform: translateY(25px) translateX(10px);
    }
}

/* Main Container */
.container {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.company-name {
    font-size: 3.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #a855f7, #ec4899, #3b82f6);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 10px;
    animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        filter: drop-shadow(0 0 20px rgba(168, 85, 247, 0.5));
    }

    to {
        filter: drop-shadow(0 0 30px rgba(236, 72, 153, 0.8));
    }
}

.tagline {
    font-size: 1.2rem;
    color: #e2e8f0;
    font-weight: 400;
    opacity: 0.9;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero {
    text-align: center;
    max-width: 800px;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.highlight {
    background: linear-gradient(135deg, #a855f7, #ec4899);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.hero-description {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #cbd5e1;
    margin-bottom: 40px;
}

/* Features */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.feature {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.3);
}

.feature-icon {
    font-size: 2rem;
    margin-bottom: 15px;
}

.feature h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #f1f5f9;
}

.feature p {
    color: #cbd5e1;
    font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
    margin-top: 40px;
}

.coming-soon {
    margin-bottom: 30px;
}

.coming-soon h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #a855f7;
    margin-bottom: 10px;
}

.coming-soon p {
    color: #cbd5e1;
    font-size: 1rem;
}

.cta-button {
    background: linear-gradient(135deg, #a855f7, #ec4899);
    border: none;
    padding: 15px 40px;
    border-radius: 50px;
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(168, 85, 247, 0.4);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(168, 85, 247, 0.6);
}

/* Footer */
.footer {
    padding: 30px 0;
    text-align: center;
    color: #64748b;
    font-size: 0.9rem;
}

/* Responsive Design */

/* Large Desktop (1200px+) */
@media (min-width: 1200px) {
    .floating-object {
        padding: 18px 25px;
        font-size: 16px;
    }

    .obj-icon {
        font-size: 24px;
    }

    /* Enhanced animations for larger screens */
    @keyframes float1 {

        0%,
        100% {
            transform: translateY(0px) rotate(0deg);
        }

        33% {
            transform: translateY(-40px) rotate(6deg);
        }

        66% {
            transform: translateY(25px) rotate(-4deg);
        }
    }

    @keyframes float3 {

        0%,
        100% {
            transform: translateY(0px) translateX(0px);
        }

        25% {
            transform: translateY(-25px) translateX(-20px);
        }

        75% {
            transform: translateY(30px) translateX(15px);
        }
    }
}

/* Tablet (768px - 1199px) */
@media (max-width: 1199px) and (min-width: 769px) {
    .floating-object {
        padding: 12px 18px;
        font-size: 13px;
    }

    .obj-icon {
        font-size: 18px;
    }

    /* Adjusted positions for tablet */
    .influencer-obj {
        top: 12%;
        left: 5%;
    }

    .brands-obj {
        top: 30%;
        left: 78%;
    }

    /* Reduced animation intensity */
    @keyframes float1 {

        0%,
        100% {
            transform: translateY(0px) rotate(0deg);
        }

        33% {
            transform: translateY(-20px) rotate(3deg);
        }

        66% {
            transform: translateY(15px) rotate(-2deg);
        }
    }

    @keyframes float3 {

        0%,
        100% {
            transform: translateY(0px) translateX(0px);
        }

        25% {
            transform: translateY(-15px) translateX(-10px);
        }

        75% {
            transform: translateY(18px) translateX(8px);
        }
    }
}

/* Mobile (768px and below) */
@media (max-width: 768px) {
    .company-name {
        font-size: 2.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .features {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    /* Mobile-optimized floating objects */
    .floating-object {
        padding: 8px 12px;
        font-size: 11px;
        border-radius: 15px;
    }

    .obj-icon {
        font-size: 14px;
    }

    /* Repositioned for mobile */
    .influencer-obj {
        top: 8%;
        left: 3%;
    }

    .brands-obj {
        top: 25%;
        left: 70%;
    }

    /* Subtle animations for mobile */
    @keyframes float1 {

        0%,
        100% {
            transform: translateY(0px);
        }

        50% {
            transform: translateY(-10px);
        }
    }

    @keyframes float3 {

        0%,
        100% {
            transform: translateY(0px) translateX(0px);
        }

        25% {
            transform: translateY(-8px) translateX(-5px);
        }

        75% {
            transform: translateY(10px) translateX(3px);
        }
    }
}

/* Small Mobile (480px and below) */
@media (max-width: 480px) {
    .floating-object {
        padding: 6px 10px;
        font-size: 10px;
        border-radius: 12px;
    }

    .obj-icon {
        font-size: 12px;
    }

    .obj-content {
        gap: 6px;
    }

    /* Minimal positioning for very small screens */
    .influencer-obj {
        top: 5%;
        left: 2%;
    }

    .brands-obj {
        top: 20%;
        left: 65%;
    }

    /* Very subtle animations */
    .floating-object {
        animation-duration: 25s;
    }

    @keyframes float1 {

        0%,
        100% {
            transform: translateY(0px);
        }

        50% {
            transform: translateY(-6px);
        }
    }

    @keyframes float3 {

        0%,
        100% {
            transform: translateY(0px);
        }

        50% {
            transform: translateY(-6px) translateX(-3px);
        }
    }
}
